import { <PERSON>ada<PERSON> } from 'next'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Blog - Arkit Karmokar (arkit-k) | Software Engineering Insights',
  description: 'Read the latest articles and insights from Arkit Karmokar (arkit-k) about software engineering, full stack development, and modern web technologies.',
  keywords: [
    'Arkit Karmokar blog',
    'arkit-k articles',
    'software engineering blog',
    'full stack development',
    'web development tutorials',
    'React tutorials',
    'Next.js guides',
    'TypeScript tips'
  ],
  openGraph: {
    title: 'Blog - Arkit Karmokar (arkit-k) | Software Engineering Insights',
    description: 'Read the latest articles and insights from Arkit Karmokar about software engineering and web development.',
    url: 'https://arkit.dev/blog',
    type: 'website',
  },
}

// Sample blog posts data
const blogPosts = [
  {
    id: 1,
    title: 'Building Scalable React Applications with TypeScript',
    excerpt: 'Learn how to structure and build maintainable React applications using TypeScript, best practices, and modern development patterns.',
    date: '2024-12-15',
    readTime: '8 min read',
    slug: 'building-scalable-react-applications-typescript',
    tags: ['React', 'TypeScript', 'Web Development']
  },
  {
    id: 2,
    title: 'Full Stack Development with Next.js and Node.js',
    excerpt: 'A comprehensive guide to building full stack applications using Next.js for the frontend and Node.js for the backend.',
    date: '2024-12-10',
    readTime: '12 min read',
    slug: 'full-stack-development-nextjs-nodejs',
    tags: ['Next.js', 'Node.js', 'Full Stack']
  },
  {
    id: 3,
    title: 'Database Design Patterns for Modern Web Applications',
    excerpt: 'Explore effective database design patterns and best practices for PostgreSQL and MongoDB in modern web applications.',
    date: '2024-12-05',
    readTime: '10 min read',
    slug: 'database-design-patterns-web-applications',
    tags: ['Database', 'PostgreSQL', 'MongoDB']
  }
]

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-12">
          <Link 
            href="/"
            className="inline-flex items-center text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Portfolio
          </Link>
          
          <h1 className="text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
            Blog by Arkit Karmokar (arkit-k)
          </h1>
          <p className="text-lg text-deep-charcoal/80 dark:text-dark-text/80 max-w-2xl">
            Insights, tutorials, and thoughts on software engineering, full stack development, 
            and modern web technologies from <strong>Arkit Karmokar</strong> (arkit-k).
          </p>
        </div>

        {/* Blog Posts */}
        <div className="space-y-8">
          {blogPosts.map((post) => (
            <article 
              key={post.id}
              className="bg-white dark:bg-dark-surface rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex items-center text-sm text-deep-charcoal/60 dark:text-dark-text/60 mb-3">
                <Calendar className="w-4 h-4 mr-1" />
                <span className="mr-4">{new Date(post.date).toLocaleDateString()}</span>
                <Clock className="w-4 h-4 mr-1" />
                <span>{post.readTime}</span>
              </div>
              
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-3 hover:text-accent-green transition-colors">
                <Link href={`/blog/${post.slug}`}>
                  {post.title}
                </Link>
              </h2>
              
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 mb-4 leading-relaxed">
                {post.excerpt}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.map((tag) => (
                  <span 
                    key={tag}
                    className="px-3 py-1 text-xs font-medium bg-accent-green/10 text-accent-green rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              <Link 
                href={`/blog/${post.slug}`}
                className="text-accent-green hover:text-accent-green/80 font-medium transition-colors"
              >
                Read more →
              </Link>
            </article>
          ))}
        </div>

        {/* Coming Soon */}
        <div className="mt-12 text-center">
          <p className="text-deep-charcoal/60 dark:text-dark-text/60">
            More articles coming soon! Follow <strong>arkit-k</strong> for updates.
          </p>
        </div>
      </div>
    </div>
  )
}
