import Image from "next/image";
import Rust from '../../../public/rust.svg';
import {
  Code2,
  Database,
  Container,
  Cloud,
  Heart,
  FileText,
} from "lucide-react";


// Technology icons component
const TechIcon = ({ name }: { name: string }) => {
  const iconMap = {
    'React': (
      <svg className="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 10.11c1.03 0 1.87.84 1.87 1.89s-.84 1.89-1.87 1.89c-1.03 0-1.87-.84-1.87-1.89s.84-1.89 1.87-1.89M7.37 20c.63.38 2.01-.2 3.6-1.7-.52-.59-1.03-1.23-1.51-1.9a22.7 22.7 0 0 1-2.4-.36c-.51 2.14-.32 3.61.31 3.96m.71-5.74l-.29-.51c-.11.29-.22.58-.29.86.27.06.57.11.88.16l-.3-.51m6.54-.76l.81-1.5-.81-1.5c-.3-.53-.62-1-.91-1.47C13.17 9 12.6 9 12 9s-1.17 0-1.71.03c-.29.47-.61.94-.91 1.47L8.57 12l.81 1.5c.3.53.62 1 .91 1.47.54.03 1.11.03 1.71.03s1.17 0 1.71-.03c.29-.47.61-.94.91-1.47M12 6.78c-.19.22-.39.45-.59.72h1.18c-.2-.27-.4-.5-.59-.72m0 10.44c.19-.22.39-.45.59-.72h-1.18c.2.27.4.5.59.72M16.62 4c-.62-.38-2 .2-3.59 1.7.52.59 1.03 1.23 1.51 1.9.82.08 1.63.2 2.4.36.51-2.14.32-3.61-.32-3.96m-.7 5.74l.29.51c.11-.29.22-.58.29-.86-.27-.06-.57-.11-.88-.16l.3.51m1.45-7.05c1.47.84 1.63 3.05 1.01 5.63 2.54.75 4.37 1.99 4.37 3.68s-1.83 2.93-4.37 3.68c.62 2.58.46 4.79-1.01 5.63-1.46.84-3.45-.12-5.37-1.95-1.92 1.83-3.91 2.79-5.37 1.95-1.47-.84-1.63-3.05-1.01-5.63-2.54-.75-4.37-1.99-4.37-3.68s1.83-2.93 4.37-3.68c-.62-2.58-.46-4.79 1.01-5.63 1.46-.84 3.45.12 5.37 1.95 1.92-1.83 3.91-2.79 5.37-1.95z"/>
      </svg>
    ),
    'Rust': (
          <svg 
      className="w-full h-full" 
      viewBox="0 0 122.88 122.88" 
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M101.44,46.25c0.82-1.84,2.97-2.68,4.81-1.86c1.84,0.82,2.67,2.98,1.86,4.82c-0.82,1.85-2.97,2.68-4.81,1.86 C101.45,50.25,100.62,48.1,101.44,46.25L101.44,46.25z M58.73,11.88c1.46-1.39,3.77-1.34,5.16,0.12c1.39,1.46,1.34,3.77-0.12,5.17 c-1.46,1.39-3.76,1.34-5.16-0.12C57.22,15.58,57.27,13.27,58.73,11.88L58.73,11.88z M106.36,61.05c0,1.12-0.04,2.23-0.12,3.34 h-4.61c-0.46,0-0.65,0.3-0.65,0.75v2.12c0,4.98-2.81,6.07-5.27,6.34c-2.34,0.26-4.94-0.98-5.26-2.42 c-1.38-7.78-3.69-9.44-7.33-12.31c4.52-2.87,9.22-7.1,9.22-12.76c0-6.12-4.19-9.97-7.05-11.86c-4.01-2.64-8.45-3.17-9.65-3.17 H27.97c6.46-7.21,15.22-12.32,25.13-14.18l5.62,5.89c1.27,1.33,3.37,1.38,4.7,0.11l6.29-6.01c13.17,2.45,24.33,10.65,30.75,21.9 l-4.3,9.72c-0.74,1.68,0.02,3.65,1.69,4.4l8.29,3.68C106.29,58.05,106.36,59.54,106.36,61.05L106.36,61.05z M51.26,53.43v-8.46 h15.16c0.78,0,5.53,0.91,5.53,4.45c0,2.95-3.64,4-6.63,4L51.26,53.43L51.26,53.43L51.26,53.43z M16.77,56.22l7.86-3.49 c1.68-0.75,2.44-2.71,1.69-4.4l-1.62-3.66h6.37v28.7H18.23c-1.11-3.92-1.71-8.05-1.71-12.32C16.51,59.41,16.6,57.8,16.77,56.22 L16.77,56.22z M21.05,46.07c0.82,1.84-0.01,4.01-1.86,4.82c-1.84,0.82-4-0.01-4.81-1.86c-0.82-1.85,0.01-4,1.86-4.82 C18.08,43.39,20.24,44.23,21.05,46.07L21.05,46.07z M34.98,102.55c-1.97,0.43-3.91-0.83-4.33-2.8c-0.42-1.98,0.83-3.92,2.8-4.34 c1.97-0.42,3.91,0.83,4.33,2.81C38.2,100.18,36.95,102.13,34.98,102.55L34.98,102.55z M85.76,90.87c-1.8-0.39-3.57,0.76-3.95,2.56 l-1.83,8.55c-5.65,2.57-11.93,3.99-18.54,3.99c-6.76,0-13.17-1.5-18.93-4.17l-1.83-8.55c-0.39-1.8-2.15-2.94-3.95-2.56l-7.55,1.62 c-1.4-1.44-2.7-2.98-3.9-4.6H62c0.42,0,0.69-0.08,0.69-0.45V74.27c0-0.38-0.28-0.45-0.69-0.45H51.26v-8.23h11.61 c1.06,0,5.67,0.3,7.14,6.19c0.46,1.81,1.47,7.7,2.17,9.59c0.69,2.12,3.5,6.34,6.5,6.34h18.29c0.21,0,0.43-0.02,0.66-0.07 c-1.27,1.72-2.66,3.36-4.16,4.88L85.76,90.87L85.76,90.87z M87.51,102.73c-1.97-0.43-3.22-2.37-2.8-4.34 c0.42-1.97,2.36-3.23,4.33-2.8c1.97,0.42,3.23,2.37,2.8,4.34C91.42,101.89,89.48,103.15,87.51,102.73L87.51,102.73z M122.04,59.93 l-5.16-3.19c-0.04-0.5-0.09-1-0.15-1.5l4.43-4.13c0.45-0.42,0.65-1.04,0.53-1.64c-0.12-0.6-0.54-1.1-1.12-1.32l-5.67-2.12 c-0.14-0.49-0.29-0.98-0.44-1.46l3.53-4.91c0.36-0.5,0.43-1.15,0.2-1.72c-0.23-0.57-0.75-0.97-1.35-1.07l-5.98-0.97 c-0.23-0.45-0.47-0.9-0.72-1.34l2.51-5.51c0.26-0.56,0.2-1.21-0.14-1.72c-0.34-0.51-0.92-0.81-1.54-0.79l-6.07,0.21 c-0.31-0.39-0.63-0.78-0.96-1.16l1.39-5.91c0.14-0.6-0.04-1.23-0.47-1.66c-0.43-0.43-1.06-0.61-1.66-0.47l-5.91,1.39 c-0.38-0.32-0.77-0.64-1.16-0.96l0.21-6.07c0.02-0.61-0.28-1.2-0.79-1.54c-0.51-0.34-1.16-0.39-1.72-0.14l-5.51,2.51 c-0.44-0.24-0.89-0.49-1.34-0.72l-0.97-5.98c-0.1-0.61-0.5-1.12-1.07-1.35c-0.57-0.24-1.22-0.16-1.72,0.2l-4.91,3.54 c-0.48-0.15-0.97-0.3-1.46-0.44L74.74,2.3c-0.21-0.58-0.71-1-1.32-1.12c-0.6-0.12-1.22,0.08-1.64,0.53l-4.14,4.44 c-0.5-0.06-1-0.11-1.5-0.15l-3.19-5.16C62.63,0.32,62.06,0,61.44,0c-0.62,0-1.19,0.32-1.51,0.84L56.74,6c-0.5,0.04-1,0.09-1.5,0.15 L51.1,1.71c-0.42-0.45-1.04-0.65-1.64-0.53c-0.6,0.12-1.1,0.54-1.32,1.12l-2.12,5.67c-0.49,0.14-0.98,0.29-1.46,0.44l-4.91-3.54 c-0.5-0.36-1.15-0.44-1.72-0.2c-0.57,0.23-0.97,0.75-1.07,1.35l-0.97,5.98c-0.45,0.23-0.9,0.47-1.34,0.72l-5.51-2.51 c-0.56-0.26-1.21-0.2-1.72,0.14c-0.51,0.34-0.81,0.92-0.79,1.54l0.21,6.07c-0.39,0.31-0.78,0.63-1.16,0.96l-5.91-1.39 c-0.6-0.14-1.23,0.04-1.66,0.47c-0.44,0.44-0.61,1.06-0.47,1.66l1.39,5.91c-0.32,0.38-0.64,0.77-0.96,1.16l-6.07-0.21 c-0.61-0.02-1.2,0.28-1.54,0.79c-0.34,0.51-0.39,1.16-0.14,1.72l2.51,5.51c-0.24,0.44-0.49,0.89-0.72,1.34l-5.98,0.97 c-0.61,0.1-1.12,0.5-1.35,1.07c-0.23,0.57-0.16,1.22,0.2,1.72l3.54,4.91c-0.15,0.48-0.3,0.97-0.44,1.46L2.3,48.14 c-0.58,0.21-1,0.71-1.12,1.32c-0.12,0.6,0.08,1.22,0.53,1.64l4.43,4.13c-0.05,0.5-0.1,1-0.15,1.5l-5.16,3.19 C0.32,60.26,0,60.83,0,61.44c0,0.62,0.32,1.19,0.84,1.51L6,66.14c0.04,0.5,0.09,1,0.15,1.5l-4.43,4.14 c-0.45,0.42-0.65,1.04-0.53,1.64c0.12,0.6,0.54,1.1,1.12,1.32l5.67,2.12c0.14,0.49,0.29,0.98,0.44,1.46l-3.54,4.91 c-0.36,0.5-0.44,1.15-0.2,1.72c0.24,0.57,0.75,0.98,1.36,1.07L12.01,87c0.23,0.45,0.47,0.9,0.72,1.34l-2.51,5.51 c-0.26,0.56-0.2,1.21,0.14,1.72c0.34,0.51,0.93,0.81,1.54,0.79l6.06-0.21c0.32,0.39,0.63,0.78,0.96,1.16l-1.39,5.91 c-0.14,0.6,0.04,1.22,0.47,1.66c0.43,0.44,1.06,0.61,1.66,0.47l5.91-1.39c0.38,0.33,0.77,0.64,1.16,0.96l-0.21,6.07 c-0.02,0.61,0.28,1.2,0.79,1.54c0.51,0.34,1.16,0.39,1.72,0.14l5.51-2.51c0.44,0.25,0.89,0.49,1.34,0.72l0.97,5.97 c0.1,0.61,0.5,1.12,1.07,1.36c0.57,0.24,1.22,0.16,1.72-0.2l4.91-3.54c0.48,0.15,0.97,0.3,1.46,0.45l2.12,5.67 c0.21,0.58,0.71,1,1.32,1.12c0.6,0.12,1.22-0.08,1.64-0.53l4.14-4.43c0.5,0.06,1,0.11,1.5,0.15l3.19,5.16 c0.32,0.52,0.89,0.84,1.51,0.84c0.62,0,1.19-0.32,1.51-0.84l3.19-5.16c0.5-0.04,1-0.09,1.5-0.15l4.14,4.43 c0.42,0.45,1.04,0.65,1.64,0.53c0.6-0.12,1.1-0.54,1.32-1.12l2.12-5.67c0.49-0.14,0.98-0.29,1.46-0.45l4.91,3.54 c0.5,0.36,1.15,0.43,1.72,0.2c0.57-0.23,0.97-0.75,1.07-1.36l0.97-5.97c0.45-0.23,0.9-0.48,1.34-0.72l5.51,2.51 c0.56,0.26,1.21,0.2,1.72-0.14c0.51-0.34,0.81-0.92,0.79-1.54l-0.21-6.07c0.39-0.31,0.78-0.63,1.16-0.96l5.91,1.39 c0.6,0.14,1.23-0.03,1.66-0.47c0.44-0.44,0.61-1.06,0.47-1.66l-1.39-5.91c0.32-0.38,0.64-0.77,0.96-1.16l6.07,0.21 c0.61,0.02,1.2-0.28,1.54-0.79c0.34-0.51,0.4-1.16,0.14-1.72l-2.51-5.51c0.25-0.45,0.49-0.89,0.72-1.34l5.98-0.97 c0.61-0.1,1.12-0.5,1.35-1.07c0.24-0.57,0.16-1.22-0.2-1.72l-3.54-4.91c0.15-0.49,0.3-0.97,0.44-1.46l5.67-2.12 c0.58-0.22,1-0.71,1.12-1.32c0.12-0.6-0.08-1.22-0.53-1.64l-4.43-4.14c0.05-0.5,0.1-1,0.15-1.5l5.16-3.19 c0.52-0.32,0.84-0.89,0.84-1.51C122.88,60.83,122.56,60.26,122.04,59.93L122.04,59.93z"/>
    </svg>
    ),
    'Next.js': (
      <svg className="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
        <path d="M11.5725 0c-.1763 0-.3098.0013-.3584.0067-.0516.0053-.2159.021-.3636.0328-3.4088.3073-6.6017 2.1463-8.624 4.9728C1.1004 6.584.3802 8.3666.1082 10.255c-.0962.659-.108.8537-.108 1.7474s.012 1.0884.108 1.7476c.652 4.506 3.8591 8.2919 8.2087 9.6945.7789.2511 1.6.4223 2.5337.5255.3636.04 1.9354.04 2.299 0 1.6117-.1783 2.9772-.577 4.3237-1.2643.2065-.1056.2464-.1337.2183-.1573-.0188-.0139-.8987-1.1938-1.9543-2.62l-1.919-2.592-2.4047-3.5583c-1.3231-1.9564-2.4117-3.556-2.4211-3.556-.0094-.0026-.0187 1.5787-.0235 3.509-.0067 3.3802-.0093 3.5162-.0516 3.596-.061.115-.108.1618-.2064.2134-.075.0374-.1408.0445-.5429.0445h-.4570l-.0803-.0516c-.0516-.0336-.0939-.0822-.1145-.1262l-.0281-.0723.0188-4.6901.0235-4.6901.0375-.0751c.0233-.0516.0751-.1171.1138-.1503.0561-.047.0994-.0517.4665-.0517.4570 0 .5429.0141.6570.0938.0328.0235 1.3457 2.0186 2.915 4.4317l2.8544 4.3846 1.9107 2.9057 1.9107 2.9057.0423-.0281c.7939-.5194 1.5329-1.1477 2.1665-1.8438 1.6977-1.8729 2.7387-4.0172 3.0845-6.3581.0962-.659.108-.8537.108-1.7474s-.012-1.0884-.108-1.7476C22.8982 4.2625 19.6711.4766 15.3217-.9261c-.7672-.2487-1.5739-.4172-2.4985-.5232-.3389-.0388-1.9321-.0388-2.2710.0000z"/>
      </svg>
    ),
    'TypeScript': (
      <svg className="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
        <path d="M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"/>
      </svg>
    ),
    'Node.js': (
      <svg className="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
        <path d="M11.998,24c-0.321,0-0.641-0.084-0.922-0.247l-2.936-1.737c-0.438-0.245-0.224-0.332-0.08-0.383 c0.585-0.203,0.703-0.25,1.328-0.604c0.065-0.037,0.151-0.023,0.218,0.017l2.256,1.339c0.082,0.045,0.197,0.045,0.272,0l8.795-5.076 c0.082-0.047,0.134-0.141,0.134-0.238V6.921c0-0.099-0.053-0.192-0.137-0.242l-8.791-5.072c-0.081-0.047-0.189-0.047-0.271,0 L3.075,6.68C2.99,6.729,2.936,6.825,2.936,6.921v10.15c0,0.097,0.054,0.189,0.139,0.235l2.409,1.392 c1.307,0.654,2.108-0.116,2.108-0.89V7.787c0-0.142,0.114-0.253,0.256-0.253h1.115c0.139,0,0.255,0.112,0.255,0.253v10.021 c0,1.745-0.95,2.745-2.604,2.745c-0.508,0-0.909,0-2.026-0.551L2.28,18.675c-0.57-0.329-0.922-0.945-0.922-1.604V6.921 c0-0.659,0.353-1.275,0.922-1.603l8.795-5.082c0.557-0.315,1.296-0.315,1.848,0l8.794,5.082c0.570,0.329,0.924,0.944,0.924,1.603 v10.15c0,0.659-0.354,1.273-0.924,1.604l-8.794,5.078C12.643,23.916,12.324,24,11.998,24z M19.099,13.993 c0-1.9-1.284-2.406-3.987-2.763c-2.731-0.361-3.009-0.548-3.009-1.187c0-0.528,0.235-1.233,2.258-1.233 c1.807,0,2.473,0.389,2.747,1.607c0.024,0.115,0.129,0.199,0.247,0.199h1.141c0.071,0,0.138-0.031,0.186-0.081 c0.048-0.054,0.074-0.123,0.067-0.196c-0.177-2.098-1.571-3.076-4.388-3.076c-2.508,0-4.004,1.058-4.004,2.833 c0,1.925,1.488,2.457,3.895,2.695c2.88,0.282,3.103,0.703,3.103,1.269c0,0.983-0.789,1.402-2.642,1.402 c-2.327,0-2.839-0.584-3.011-1.742c-0.02-0.124-0.126-0.215-0.253-0.215h-1.137c-0.141,0-0.254,0.112-0.254,0.253 c0,1.482,0.806,3.248,4.655,3.248C17.501,17.007,19.099,15.91,19.099,13.993z"/>
      </svg>
    ),
    'Python': (
      <svg className="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
        <path d="M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z"/>
      </svg>
    ),
    'PostgreSQL': <Database className="w-full h-full" />,
    'Docker': <Container className="w-full h-full" />,
    'AWS': <Cloud className="w-full h-full" />,
    'MongoDB': <Database className="w-full h-full" />,

  };

  return iconMap[name as keyof typeof iconMap] || <Code2 className="w-full h-full" />;
};

export default function AboutSection() {
  return (
    <section id="about" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-start">
          {/* Left Content - About Text */}
          <div className="space-y-6">
            <div className="relative animate-fade-in">
              <h2 className="text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-8">
                About Arkit Karmokar (arkit-k)
              </h2>
            </div>

            <div className="space-y-4 text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed animate-slide-up">
              <p className="text-lg">
                I'm <strong>Arkit Karmokar</strong>, also known as <strong>arkit</strong> or <strong>arkit-k</strong>, a passionate <strong>Full Stack Developer</strong> and <strong>Software Engineer</strong> with extensive experience in modern web technologies. I specialize in creating meaningful software solutions that solve real-world problems.
              </p>

              <p className="text-lg">
                My journey in <strong>software development</strong> started with curiosity and has evolved into a mission to build innovative tools and applications. I have expertise in <strong>React</strong>, <strong>Next.js</strong>, <strong>TypeScript</strong>, <strong>Node.js</strong>, and <strong>Python</strong>, focusing on both <strong>frontend</strong> and <strong>backend development</strong>.
              </p>

              <p className="text-lg">
                As a <strong>freelance developer</strong> and <strong>software consultant</strong>, I'm available for hire to help bring your ideas to life. Whether you need a full-stack web application, API development, or technical consulting, I'm here to help.
              </p>

              <p className="text-lg">
                Beyond the technical realm, I enjoy writing about my experiences, sharing knowledge with the
                community, Every project is an opportunity to learn something
                new and push the boundaries of what's possible.
              </p>
            </div>

            {/* Skills/Interests */}
            <div className="mt-8 animate-scale-in">
              <h3 className="text-xl font-semibold text-deep-charcoal dark:text-dark-text mb-6">
                Technical Skills & Technologies - arkit-k's Expertise
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4">
                {[
                  'React', 'Next.js', 'TypeScript', 'Node.js',
                  'Python', 'PostgreSQL', 'Docker', 'AWS','MongoDB','Rust'
                ].map((skill) => (
                  <div
                    key={skill}
                    className="group flex flex-col items-center gap-2 sm:gap-3 p-3 sm:p-4 bg-accent-green/10 hover:bg-accent-green/20 text-accent-green rounded-xl border border-accent-green/20 hover:border-accent-green/40 transition-all duration-300 hover:scale-105 min-h-[80px] sm:min-h-[100px]"
                    title={skill}
                  >
                    <div className="text-accent-green group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10">
                        <TechIcon name={skill} />
                      </div>
                    </div>
                    <span className="text-xs sm:text-sm font-medium text-center leading-tight">{skill}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content - Image first, then additional content */}
          <div className="space-y-8 md:mt-25">
            {/* Character Image */}
            <div className="relative w-full h-64 lg:h-90 parallax-element md:mt-9">
              <Image
                src="/cuto-4.png"
                alt="Character with cat illustration"
                fill
                className="object-contain md:mt-9"
              />
            </div>

            {/* Additional Content Card */}
            <div className="bg-light-almond/50 dark:bg-dark-surface/50 rounded-2xl p-4 lg:p-1 animate-fade-in">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-deep-charcoal dark:text-dark-text" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Problem Solver</h4>
                    <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                      I love tackling complex challenges and finding elegant solutions
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-deep-charcoal dark:text-dark-text" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Continuous Learner</h4>
                    <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                      Always exploring new technologies and best practices
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-accent-green rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-deep-charcoal dark:text-dark-text" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99L12 11l-1.99-2.01A2.5 2.5 0 0 0 8 8H5.46c-.8 0-1.54.37-2.01.99L1 12v10h2v-6h2.5l-1.5-4.5h2L8 18h8l2-6.5h2L18.5 16H21v6h2z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-deep-charcoal dark:text-dark-text">Team Player</h4>
                    <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                      Collaboration and knowledge sharing drive the best results
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
