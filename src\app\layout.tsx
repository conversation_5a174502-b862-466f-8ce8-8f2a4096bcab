import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import FloatingMusicButton from "@/components/FloatingMusicButton";
import YouTubeMusicPlayer from "@/components/YouTubeMusicPlayer";
import { MusicProvider } from "@/components/MusicContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Arkit Karmokar (arkit-k) - Software Engineer & Full Stack Developer Portfolio",
  description: "Arkit Karmokar (arkit, arkit-k) - Software Engineer, Therapist, and creator. Explore my portfolio, projects, and more. Available for hire.",
  keywords: [
    "Arkit Karmokar",
    "arkit",
    "arkit-k",
    "arkit karmokar",
    "Software Engineer",
    "Therapist",
    "Portfolio",
    "Arkit Portfolio",
    "arkit-k portfolio",
    "Arkit-karmokar",
    "full stack developer",
    "backend developer",
    "frontend developer",
    "devops",
    "React developer",
    "Next.js developer",
    "TypeScript developer",
    "JavaScript developer",
    "Python developer",
    "Node.js developer",
    "Web developer",
    "Software development",
    "Programming",
    "Tech portfolio",
    "Developer portfolio",
    "Freelance developer",
    "Software consultant",
    "hire arkit",
    "hire arkit-k"
  ],
  authors: [{ name: "Arkit Karmokar", url: "https://arkit.dev" }],
  creator: "Arkit Karmokar",
  publisher: "Arkit Karmokar",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://arkit.dev',
    siteName: 'Arkit Karmokar Portfolio',
    title: 'Arkit Karmokar (arkit-k) - Software Engineer & Full Stack Developer',
    description: 'Software Engineer, Therapist, and creator. Explore my portfolio, projects, and more.',
    images: [
      {
        url: '/hero.png',
        width: 1200,
        height: 630,
        alt: 'Arkit Karmokar - Software Engineer Portfolio',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Arkit Karmokar (arkit-k) - Software Engineer & Full Stack Developer',
    description: 'Software Engineer, Therapist, and creator. Explore my portfolio and projects.',
    images: ['/hero.png'],
    creator: '@arkit_k',
  },
  alternates: {
    canonical: 'https://arkit.dev',
  },
  viewport: "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
      },
    ],
  },
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Enhanced SEO Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "Arkit Karmokar",
            "alternateName": ["arkit", "arkit-k", "arkit karmokar"],
            "url": "https://arkit.dev",
            "image": "https://arkit.dev/hero.png",
            "jobTitle": ["Software Engineer", "Full Stack Developer", "Therapist", "Web Developer"],
            "worksFor": {
              "@type": "Organization",
              "name": "Freelance"
            },
            "knowsAbout": [
              "Software Engineering",
              "Full Stack Development",
              "Web Development",
              "React",
              "Next.js",
              "TypeScript",
              "JavaScript",
              "Python",
              "Node.js",
              "DevOps",
              "Frontend Development",
              "Backend Development"
            ],
            "description": "Software Engineer, Therapist, and creator specializing in modern web technologies.",
            "sameAs": [
              "https://github.com/arkit-k",
              "https://cal.com/arkit-karmokar-x0uyir/secret"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "email": "<EMAIL>",
              "contactType": "professional"
            }
          })
        }} />

        {/* Website Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "Arkit Karmokar Portfolio",
            "alternateName": ["arkit portfolio", "arkit-k portfolio"],
            "url": "https://arkit.dev",
            "description": "Professional portfolio of Arkit Karmokar (arkit-k), Software Engineer and Full Stack Developer",
            "author": {
              "@type": "Person",
              "name": "Arkit Karmokar"
            },
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://arkit.dev/?search={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          })
        }} />

        {/* Professional Service Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ProfessionalService",
            "name": "Arkit Karmokar Software Development Services",
            "alternateName": ["arkit development", "arkit-k services"],
            "url": "https://arkit.dev",
            "description": "Professional software development and full stack development services by Arkit Karmokar",
            "provider": {
              "@type": "Person",
              "name": "Arkit Karmokar",
              "alternateName": ["arkit", "arkit-k"]
            },
            "areaServed": "Worldwide",
            "serviceType": [
              "Software Development",
              "Full Stack Development",
              "Web Development",
              "Frontend Development",
              "Backend Development",
              "DevOps Services"
            ]
          })
        }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <MusicProvider>
            {children}
            {/* Desktop Music Players */}
            <div className="hidden md:block">
              <FloatingMusicButton
                playlistId="37i9dQZF1DXcBWIGoYBM5M"
                playlistName="🎵 Arkit's Favorite Vibes"
                delay={3000}
              />
              <YouTubeMusicPlayer
                videoId="jfKfPfyJRdk"
                title="Arkit's Coding Vibes"
                autoShow={true}
              />
            </div>

            {/* Mobile Music Player - Integrated in MobileBottomNav */}
            <div className="md:hidden">
              {/* Mobile music is handled by MobileBottomNav component */}
            </div>
          </MusicProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
