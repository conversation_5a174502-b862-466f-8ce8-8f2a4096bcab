"use client";

import { Briefcase, Calendar, MapPin, ExternalLink } from "lucide-react";

// Work experience data
const workExperience = [
  {
    id: 1,
    company: "Freelance",
    position: "Full Stack Developer",
    location: "Remote",
    startDate: "2023",
    endDate: "Present",
    duration: "1+ years",
    description: "Developing custom web applications and software solutions for clients worldwide. Specializing in React, Next.js, Node.js, and modern web technologies.",
    responsibilities: [
      "Built responsive web applications using React and Next.js",
      "Developed RESTful APIs and backend services with Node.js",
      "Implemented database solutions with PostgreSQL and MongoDB",
      "Collaborated with clients to deliver high-quality software solutions",
      "Maintained and optimized existing applications for performance"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "MongoDB", "AWS"],
    type: "Freelance",
    website: "https://arkit-k.xyz"
  },
  {
    id: 2,
    company: "Tech Startup",
    position: "Software Engineer",
    location: "Mumbai, India",
    startDate: "2022",
    endDate: "2023",
    duration: "1 year",
    description: "Worked as a software engineer developing scalable web applications and contributing to the company's core product development.",
    responsibilities: [
      "Developed and maintained web applications using React and TypeScript",
      "Collaborated with cross-functional teams to deliver features",
      "Participated in code reviews and maintained coding standards",
      "Optimized application performance and user experience",
      "Contributed to technical documentation and best practices"
    ],
    technologies: ["React", "TypeScript", "Python", "Django", "PostgreSQL", "Docker"],
    type: "Full-time",
    website: null
  },
  {
    id: 3,
    company: "Local Development Agency",
    position: "Junior Developer",
    location: "India",
    startDate: "2021",
    endDate: "2022",
    duration: "1 year",
    description: "Started my professional journey as a junior developer, working on various client projects and learning modern web development practices.",
    responsibilities: [
      "Assisted in developing client websites and web applications",
      "Learned and implemented modern JavaScript frameworks",
      "Collaborated with senior developers on project delivery",
      "Participated in client meetings and requirement gathering",
      "Maintained and updated existing client websites"
    ],
    technologies: ["JavaScript", "React", "HTML/CSS", "Node.js", "MySQL"],
    type: "Full-time",
    website: null
  }
];

export default function WorkExperienceSection() {
  return (
    <section id="work-experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
            Work Experience
          </h2>
          <p className="text-deep-charcoal/70 dark:text-dark-text/70 text-lg">
            My professional journey as a Full Stack Developer and Software Engineer
          </p>
        </div>

        <div className="space-y-8">
          {workExperience.map((experience, index) => (
            <div
              key={experience.id}
              className="group relative"
            >
              {/* Timeline line */}
              {index !== workExperience.length - 1 && (
                <div className="absolute left-6 top-16 w-0.5 h-full bg-deep-charcoal/20 dark:bg-dark-text/20"></div>
              )}
              
              {/* Experience Card */}
              <div className="flex gap-6">
                {/* Timeline dot */}
                <div className="flex-shrink-0 w-12 h-12 bg-accent-green rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Briefcase className="w-6 h-6 text-white" />
                </div>

                {/* Content */}
                <div className="flex-1 pb-8">
                  <div className="bg-deep-charcoal/5 dark:bg-dark-text/5 rounded-lg p-6 hover:bg-deep-charcoal/10 dark:hover:bg-dark-text/10 transition-all duration-300">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-deep-charcoal dark:text-dark-text mb-1">
                          {experience.position}
                        </h3>
                        <div className="flex items-center gap-2 text-accent-green font-medium mb-2">
                          <span>{experience.company}</span>
                          {experience.website && (
                            <a
                              href={experience.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-accent-green/80 transition-colors"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col sm:items-end gap-1">
                        <div className="flex items-center gap-1 text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                          <Calendar className="w-4 h-4" />
                          <span>{experience.startDate} - {experience.endDate}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                          <MapPin className="w-4 h-4" />
                          <span>{experience.location}</span>
                        </div>
                        <span className="text-xs bg-accent-green/10 text-accent-green px-2 py-1 rounded-full">
                          {experience.type}
                        </span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-deep-charcoal/80 dark:text-dark-text/80 mb-4 leading-relaxed">
                      {experience.description}
                    </p>

                    {/* Responsibilities */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-deep-charcoal dark:text-dark-text mb-2">
                        Key Responsibilities:
                      </h4>
                      <ul className="list-disc list-inside text-sm text-deep-charcoal/70 dark:text-dark-text/70 space-y-1">
                        {experience.responsibilities.map((responsibility, idx) => (
                          <li key={idx}>{responsibility}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div>
                      <h4 className="text-sm font-semibold text-deep-charcoal dark:text-dark-text mb-2">
                        Technologies Used:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 text-xs font-medium bg-deep-charcoal/10 dark:bg-dark-text/10 text-deep-charcoal/80 dark:text-dark-text/80 rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <p className="text-deep-charcoal/70 dark:text-dark-text/70 mb-4">
            Interested in working together?
          </p>
          <a
            href="#hire"
            className="inline-flex items-center px-6 py-3 bg-accent-green hover:bg-accent-green/80 text-white font-medium rounded-lg transition-colors"
          >
            Let's Talk
          </a>
        </div>
      </div>
    </section>
  );
}
